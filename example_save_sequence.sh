#!/bin/bash
# 示例脚本：保存Gaussian点云序列为PLY文件

# 设置参数
MODEL_DIR="output/sq_02"  # 替换为你的模型目录
SMPL_PARAMS="/home/<USER>/data/SQ_02/smpl_params.npz"  # 替换为你的SMPL参数文件
OUTPUT_DIR="output/gaussian_sequence_ply"  # 输出目录

# 基本用法：保存所有帧
echo "保存所有帧的Gaussian点云..."
python save_gaussian_sequence.py \
    --model_dir $MODEL_DIR \
    --smpl_params $SMPL_PARAMS \
    --output_dir $OUTPUT_DIR \
    --color_mode position

# 保存部分帧（例如：帧0-100，每10帧保存一次）
echo "保存部分帧的Gaussian点云（每10帧一次）..."
python save_gaussian_sequence.py \
    --model_dir $MODEL_DIR \
    --smpl_params $SMPL_PARAMS \
    --output_dir ${OUTPUT_DIR}_subset \
    --frame_start 0 \
    --frame_end 100 \
    --frame_step 10 \
    --color_mode sh

# 使用不同的着色模式
echo "使用透明度着色模式..."
python save_gaussian_sequence.py \
    --model_dir $MODEL_DIR \
    --smpl_params $SMPL_PARAMS \
    --output_dir ${OUTPUT_DIR}_opacity \
    --frame_start 0 \
    --frame_end 50 \
    --color_mode opacity

echo "完成！"
