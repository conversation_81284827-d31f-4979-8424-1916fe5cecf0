#!/usr/bin/env python3
"""
导出渲染就绪的Gaussian Splatting PLY文件
使用与render()方法完全相同的参数和变换
"""

import os
import sys
import torch
import numpy as np
from argparse import ArgumentParser

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from plyfile import PlyData, PlyElement
except ImportError:
    print("Error: plyfile package is required. Install with: pip install plyfile")
    sys.exit(1)


def extract_render_params_from_covariance(covars, means):
    """
    从协方差矩阵中提取缩放和旋转参数
    这是标准3DGS的逆过程
    """
    # 对协方差矩阵进行特征值分解
    eigenvalues, eigenvectors = torch.linalg.eigh(covars)
    
    # 确保特征值为正
    eigenvalues = torch.clamp(eigenvalues, min=1e-6)
    
    # 缩放 = sqrt(特征值)
    scales = torch.sqrt(eigenvalues)
    
    # 旋转矩阵 = 特征向量矩阵
    # 确保是右手坐标系
    det = torch.det(eigenvectors)
    eigenvectors = torch.where(det.unsqueeze(-1).unsqueeze(-1) < 0, 
                              -eigenvectors, eigenvectors)
    
    # 将旋转矩阵转换为四元数
    rotations = matrix_to_quaternion(eigenvectors)
    
    return scales, rotations


def matrix_to_quaternion(rotation_matrices):
    """
    将旋转矩阵转换为四元数 (w, x, y, z)
    """
    batch_size = rotation_matrices.shape[0]
    
    # 使用Shepperd方法转换旋转矩阵到四元数
    trace = rotation_matrices[:, 0, 0] + rotation_matrices[:, 1, 1] + rotation_matrices[:, 2, 2]
    
    quaternions = torch.zeros(batch_size, 4, device=rotation_matrices.device)
    
    # Case 1: trace > 0
    mask1 = trace > 0
    if mask1.any():
        s = torch.sqrt(trace[mask1] + 1.0) * 2  # s = 4 * qw
        quaternions[mask1, 0] = 0.25 * s  # qw
        quaternions[mask1, 1] = (rotation_matrices[mask1, 2, 1] - rotation_matrices[mask1, 1, 2]) / s  # qx
        quaternions[mask1, 2] = (rotation_matrices[mask1, 0, 2] - rotation_matrices[mask1, 2, 0]) / s  # qy
        quaternions[mask1, 3] = (rotation_matrices[mask1, 1, 0] - rotation_matrices[mask1, 0, 1]) / s  # qz
    
    # Case 2: R[0,0] > R[1,1] and R[0,0] > R[2,2]
    mask2 = (~mask1) & (rotation_matrices[:, 0, 0] > rotation_matrices[:, 1, 1]) & (rotation_matrices[:, 0, 0] > rotation_matrices[:, 2, 2])
    if mask2.any():
        s = torch.sqrt(1.0 + rotation_matrices[mask2, 0, 0] - rotation_matrices[mask2, 1, 1] - rotation_matrices[mask2, 2, 2]) * 2
        quaternions[mask2, 0] = (rotation_matrices[mask2, 2, 1] - rotation_matrices[mask2, 1, 2]) / s  # qw
        quaternions[mask2, 1] = 0.25 * s  # qx
        quaternions[mask2, 2] = (rotation_matrices[mask2, 0, 1] + rotation_matrices[mask2, 1, 0]) / s  # qy
        quaternions[mask2, 3] = (rotation_matrices[mask2, 0, 2] + rotation_matrices[mask2, 2, 0]) / s  # qz
    
    # Case 3: R[1,1] > R[2,2]
    mask3 = (~mask1) & (~mask2) & (rotation_matrices[:, 1, 1] > rotation_matrices[:, 2, 2])
    if mask3.any():
        s = torch.sqrt(1.0 + rotation_matrices[mask3, 1, 1] - rotation_matrices[mask3, 0, 0] - rotation_matrices[mask3, 2, 2]) * 2
        quaternions[mask3, 0] = (rotation_matrices[mask3, 0, 2] - rotation_matrices[mask3, 2, 0]) / s  # qw
        quaternions[mask3, 1] = (rotation_matrices[mask3, 0, 1] + rotation_matrices[mask3, 1, 0]) / s  # qx
        quaternions[mask3, 2] = 0.25 * s  # qy
        quaternions[mask3, 3] = (rotation_matrices[mask3, 1, 2] + rotation_matrices[mask3, 2, 1]) / s  # qz
    
    # Case 4: else
    mask4 = (~mask1) & (~mask2) & (~mask3)
    if mask4.any():
        s = torch.sqrt(1.0 + rotation_matrices[mask4, 2, 2] - rotation_matrices[mask4, 0, 0] - rotation_matrices[mask4, 1, 1]) * 2
        quaternions[mask4, 0] = (rotation_matrices[mask4, 1, 0] - rotation_matrices[mask4, 0, 1]) / s  # qw
        quaternions[mask4, 1] = (rotation_matrices[mask4, 0, 2] + rotation_matrices[mask4, 2, 0]) / s  # qx
        quaternions[mask4, 2] = (rotation_matrices[mask4, 1, 2] + rotation_matrices[mask4, 2, 1]) / s  # qy
        quaternions[mask4, 3] = 0.25 * s  # qz
    
    # 归一化四元数
    quaternions = quaternions / torch.norm(quaternions, dim=1, keepdim=True)
    
    return quaternions


def save_render_ready_ply(gaussians, output_path, cam_pos=None):
    """
    保存渲染就绪的PLY文件，使用与render()完全相同的参数
    """
    
    with torch.no_grad():
        # 1. 获取全局变换后的位置 (与render中的means相同)
        xyz = gaussians.get_xyz.detach().cpu().numpy()
        
        # 2. 获取处理后的透明度 (与render中的opacities相同)
        opacities = gaussians.get_opacity.detach().cpu().numpy()
        
        # 3. 获取协方差矩阵 (与render中的covars相同)
        covars = gaussians.get_covariance(scaling_modifier=1.0)
        
        # 4. 从协方差矩阵中提取缩放和旋转
        scales, rotations = extract_render_params_from_covariance(covars, gaussians.get_xyz)
        scales = scales.detach().cpu().numpy()
        rotations = rotations.detach().cpu().numpy()
        
        # 5. 获取颜色 (与render中的colors相同)
        if cam_pos is None:
            # 使用默认相机位置
            cam_pos = torch.tensor([0.0, 0.0, 3.0], device=gaussians.get_xyz.device)
        else:
            cam_pos = torch.tensor(cam_pos, device=gaussians.get_xyz.device)
        
        colors = gaussians.get_color(cam_pos).detach().cpu().numpy()
        
        print(f"渲染就绪参数统计:")
        print(f"  位置: {xyz.shape}, 范围: [{xyz.min():.4f}, {xyz.max():.4f}]")
        print(f"  透明度: {opacities.shape}, 范围: [{opacities.min():.4f}, {opacities.max():.4f}]")
        print(f"  缩放: {scales.shape}, 范围: [{scales.min():.6f}, {scales.max():.6f}]")
        print(f"  旋转: {rotations.shape}, 四元数模长范围: [{np.linalg.norm(rotations, axis=1).min():.4f}, {np.linalg.norm(rotations, axis=1).max():.4f}]")
        print(f"  颜色: {colors.shape}, 范围: [{colors.min():.4f}, {colors.max():.4f}]")
        
        # 创建零法向量
        normals = np.zeros_like(xyz)
        
        # 将颜色转换为球谐DC分量
        SH_C0 = 0.28209479177387814
        f_dc = (colors - 0.5) / SH_C0
        
        # 构建PLY属性
        dtype_list = [
            ('x', 'f4'), ('y', 'f4'), ('z', 'f4'),
            ('nx', 'f4'), ('ny', 'f4'), ('nz', 'f4'),
            ('f_dc_0', 'f4'), ('f_dc_1', 'f4'), ('f_dc_2', 'f4'),
            ('opacity', 'f4'),
            ('scale_0', 'f4'), ('scale_1', 'f4'), ('scale_2', 'f4'),
            ('rot_0', 'f4'), ('rot_1', 'f4'), ('rot_2', 'f4'), ('rot_3', 'f4')
        ]
        
        # 创建顶点数据
        vertex_data = np.empty(len(xyz), dtype=dtype_list)
        
        # 填充数据
        vertex_data['x'] = xyz[:, 0]
        vertex_data['y'] = xyz[:, 1]
        vertex_data['z'] = xyz[:, 2]
        vertex_data['nx'] = normals[:, 0]
        vertex_data['ny'] = normals[:, 1]
        vertex_data['nz'] = normals[:, 2]
        vertex_data['f_dc_0'] = f_dc[:, 0]
        vertex_data['f_dc_1'] = f_dc[:, 1]
        vertex_data['f_dc_2'] = f_dc[:, 2]
        vertex_data['opacity'] = opacities.flatten()
        vertex_data['scale_0'] = scales[:, 0]
        vertex_data['scale_1'] = scales[:, 1]
        vertex_data['scale_2'] = scales[:, 2]
        vertex_data['rot_0'] = rotations[:, 0]
        vertex_data['rot_1'] = rotations[:, 1]
        vertex_data['rot_2'] = rotations[:, 2]
        vertex_data['rot_3'] = rotations[:, 3]
        
        # 保存PLY文件
        vertex_element = PlyElement.describe(vertex_data, 'vertex')
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        PlyData([vertex_element]).write(output_path)
        
        print(f"保存渲染就绪PLY: {output_path}")


def main():
    parser = ArgumentParser(description="导出渲染就绪的Gaussian Splatting PLY文件")
    parser.add_argument('--model_dir', type=str, required=True, help='模型目录')
    parser.add_argument('--output_dir', type=str, required=True, help='输出目录')
    parser.add_argument('--smpl_params', type=str, help='SMPL参数文件')
    parser.add_argument('--frames', type=str, default='0', help='帧索引，逗号分隔 (默认: 0)')
    parser.add_argument('--cam_pos', type=str, default='0,0,3', help='相机位置 x,y,z (默认: 0,0,3)')
    
    args = parser.parse_args()
    
    # 解析参数
    frame_indices = [int(x.strip()) for x in args.frames.split(',')]
    cam_pos = [float(x.strip()) for x in args.cam_pos.split(',')]
    
    try:
        from scene.gaussian_model import GaussianModel
        from utils.smpl_utils import init_smpl
        from omegaconf import OmegaConf
        
        print(f"加载模型: {args.model_dir}")
        
        # 加载配置和模型
        config_path = os.path.join(args.model_dir, 'config.yaml')
        config = OmegaConf.load(config_path)
        
        smpl_pkl_path = config.smpl_pkl_path
        if not os.path.isabs(smpl_pkl_path):
            smpl_pkl_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), smpl_pkl_path)
        init_smpl(smpl_pkl_path)
        
        gaussians = GaussianModel()
        checkpoint_files = [f for f in os.listdir(args.model_dir) if f.startswith('chkpnt') and f.endswith('.pth')]
        checkpoint_files.sort(key=lambda x: int(x.replace('chkpnt', '').replace('.pth', '')))
        latest_checkpoint = checkpoint_files[-1]
        checkpoint_path = os.path.join(args.model_dir, latest_checkpoint)
        
        print(f"加载checkpoint: {latest_checkpoint}")
        checkpoint = torch.load(checkpoint_path, map_location='cuda', weights_only=False)
        gaussians.restore(checkpoint)
        gaussians.is_test = True
        gaussians.prepare_test()
        
        os.makedirs(args.output_dir, exist_ok=True)
        
        if args.smpl_params:
            # 加载SMPL参数并导出特定帧
            smpl_data = np.load(args.smpl_params, allow_pickle=True)
            poses, transl, expressions, jaw_poses = gaussians._parse_smpl_params(smpl_data)
            
            for frame_idx in frame_indices:
                print(f"\n处理帧 {frame_idx}...")
                gaussians._set_frame_params(
                    poses[frame_idx], transl[frame_idx],
                    expressions[frame_idx] if expressions is not None else None,
                    jaw_poses[frame_idx] if jaw_poses is not None else None
                )
                
                output_path = os.path.join(args.output_dir, f"render_ready_frame_{frame_idx:06d}.ply")
                save_render_ready_ply(gaussians, output_path, cam_pos)
        else:
            # 只导出canonical pose
            output_path = os.path.join(args.output_dir, "render_ready_canonical.ply")
            save_render_ready_ply(gaussians, output_path, cam_pos)
        
        print(f"\n完成! 文件保存在: {args.output_dir}")
        print("这些PLY文件包含与渲染器完全相同的参数，应该能正确显示。")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
