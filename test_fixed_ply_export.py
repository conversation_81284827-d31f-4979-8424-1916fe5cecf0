#!/usr/bin/env python3
"""
测试修复后的PLY导出功能
"""

import os
import sys
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_fixed_export():
    """测试修复后的导出功能"""
    print("测试修复后的Gaussian Splatting PLY导出...")
    
    # 使用修复后的导出脚本
    cmd = """python export_npz_to_ply.py \
        --mode gaussian \
        --model_dir output/sq_02 \
        --smpl_params /home/<USER>/data/SQ_02/smpl_params.npz \
        --output_dir output/gaussian_ply_fixed \
        --frame_start 0 \
        --frame_end 3 \
        --frame_step 1 \
        --format_type standard \
        --color_mode sh"""
    
    print("执行命令:")
    print(cmd)
    print("\n" + "="*50)
    
    # 执行导出
    os.system(cmd)
    
    # 检查结果
    output_dir = "output/gaussian_ply_fixed"
    if os.path.exists(output_dir):
        ply_files = [f for f in os.listdir(output_dir) if f.endswith('.ply')]
        print(f"\n生成了 {len(ply_files)} 个PLY文件:")
        for f in sorted(ply_files):
            file_path = os.path.join(output_dir, f)
            size_mb = os.path.getsize(file_path) / (1024*1024)
            print(f"  {f}: {size_mb:.1f} MB")
        
        # 检查第一个文件的头部
        if ply_files:
            first_file = os.path.join(output_dir, sorted(ply_files)[0])
            print(f"\n检查文件头部: {sorted(ply_files)[0]}")
            with open(first_file, 'rb') as f:
                # 读取头部
                header_lines = []
                while True:
                    line = f.readline().decode('ascii', errors='ignore').strip()
                    header_lines.append(line)
                    if line == 'end_header':
                        break
                    if len(header_lines) > 50:  # 防止无限循环
                        break
                
                print("PLY头部:")
                for line in header_lines:
                    print(f"  {line}")
    else:
        print("导出失败，输出目录不存在")


def analyze_gaussian_attributes():
    """分析Gaussian属性的数值范围"""
    print("\n" + "="*50)
    print("分析Gaussian属性数值范围...")
    
    try:
        from scene.gaussian_model import GaussianModel
        from utils.smpl_utils import init_smpl
        from omegaconf import OmegaConf
        import torch
        
        # 加载模型
        config_path = "output/sq_02/config.yaml"
        config = OmegaConf.load(config_path)
        
        smpl_pkl_path = config.smpl_pkl_path
        if not os.path.isabs(smpl_pkl_path):
            smpl_pkl_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), smpl_pkl_path)
        
        init_smpl(smpl_pkl_path)
        
        gaussians = GaussianModel()
        checkpoint = torch.load("output/sq_02/chkpnt20000.pth", map_location='cuda', weights_only=False)
        gaussians.restore(checkpoint)
        gaussians.is_test = True
        gaussians.prepare_test()
        
        # 分析属性
        with torch.no_grad():
            opacity = gaussians.get_opacity.detach().cpu().numpy()
            scale = gaussians.get_cano_scaling.detach().cpu().numpy()
            rotation = gaussians.get_cano_rotation.detach().cpu().numpy()
            
            print(f"原始属性统计:")
            print(f"  Opacity: min={opacity.min():.4f}, max={opacity.max():.4f}, mean={opacity.mean():.4f}")
            print(f"  Scale: min={scale.min():.4f}, max={scale.max():.4f}, mean={scale.mean():.4f}")
            print(f"  Rotation norm: min={np.linalg.norm(rotation, axis=1).min():.4f}, max={np.linalg.norm(rotation, axis=1).max():.4f}")
            
            # 应用修复
            opacity_fixed, scale_fixed, rotation_fixed = gaussians._fix_gaussian_attributes(opacity, scale, rotation)
            
            print(f"\n修复后属性统计:")
            print(f"  Opacity: min={opacity_fixed.min():.4f}, max={opacity_fixed.max():.4f}, mean={opacity_fixed.mean():.4f}")
            print(f"  Scale: min={scale_fixed.min():.4f}, max={scale_fixed.max():.4f}, mean={scale_fixed.mean():.4f}")
            print(f"  Rotation norm: min={np.linalg.norm(rotation_fixed, axis=1).min():.4f}, max={np.linalg.norm(rotation_fixed, axis=1).max():.4f}")
            
    except Exception as e:
        print(f"分析失败: {e}")


def create_comparison_exports():
    """创建对比导出：修复前vs修复后"""
    print("\n" + "="*50)
    print("创建对比导出...")
    
    # 导出简化格式（作为对比）
    cmd_simple = """python export_npz_to_ply.py \
        --mode gaussian \
        --model_dir output/sq_02 \
        --smpl_params /home/<USER>/data/SQ_02/smpl_params.npz \
        --output_dir output/gaussian_ply_simple_comparison \
        --frame_start 0 \
        --frame_end 2 \
        --frame_step 1 \
        --format_type simple \
        --color_mode sh"""
    
    print("导出简化格式用于对比...")
    os.system(cmd_simple)
    
    # 检查文件大小对比
    dirs_to_check = [
        ("output/gaussian_ply_fixed", "修复后标准格式"),
        ("output/gaussian_ply_simple_comparison", "简化格式"),
        ("output/gaussian_ply_standard", "原始标准格式")
    ]
    
    print("\n文件大小对比:")
    for dir_path, desc in dirs_to_check:
        if os.path.exists(dir_path):
            ply_files = [f for f in os.listdir(dir_path) if f.endswith('.ply')]
            if ply_files:
                first_file = os.path.join(dir_path, sorted(ply_files)[0])
                size_mb = os.path.getsize(first_file) / (1024*1024)
                print(f"  {desc}: {size_mb:.1f} MB")


def main():
    """主函数"""
    print("Gaussian Splatting PLY导出修复测试")
    print("="*60)
    
    # 测试修复后的导出
    test_fixed_export()
    
    # 分析属性
    analyze_gaussian_attributes()
    
    # 创建对比
    create_comparison_exports()
    
    print("\n" + "="*60)
    print("测试完成！")
    print("\n主要修复:")
    print("✓ 透明度: 应用sigmoid激活，确保在[0,1]范围")
    print("✓ 缩放: 限制最大值为0.1，避免过大的球")
    print("✓ 旋转: 四元数归一化，确保正确的旋转")
    print("\n建议:")
    print("1. 使用修复后的标准格式文件 (output/gaussian_ply_fixed/)")
    print("2. 如果仍有问题，可以进一步调整缩放限制")
    print("3. 在Gaussian Splatting查看器中测试显示效果")


if __name__ == "__main__":
    main()
