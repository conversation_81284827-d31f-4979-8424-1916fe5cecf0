#!/usr/bin/env python3
"""
直接保存渲染前的原始Gaussian参数
不做任何变换，保持原始状态
"""

import os
import sys
import torch
import numpy as np
from argparse import ArgumentParser

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from plyfile import PlyData, PlyElement
except ImportError:
    print("Error: plyfile package is required. Install with: pip install plyfile")
    sys.exit(1)


def save_raw_gaussian_ply(gaussians, output_path):
    """
    直接保存原始Gaussian参数，不做任何处理
    """
    
    with torch.no_grad():
        # 直接获取原始参数，不经过任何get_xxx方法
        xyz = gaussians._xyz.detach().cpu().numpy()  # 原始位置
        
        # 原始球谐系数
        sh0 = gaussians._sh0.detach().cpu().numpy()  # [N, 1, 3]
        shN = gaussians._shN.detach().cpu().numpy()  # [N, 3, 3] 
        
        # 原始其他参数
        opacity = gaussians._opacity.detach().cpu().numpy()  # 原始透明度
        scaling = gaussians._scaling.detach().cpu().numpy()  # 原始缩放
        rotation = gaussians._rotation.detach().cpu().numpy()  # 原始旋转
        
        print(f"原始参数统计:")
        print(f"  位置: {xyz.shape}, 范围: [{xyz.min():.4f}, {xyz.max():.4f}]")
        print(f"  SH0: {sh0.shape}, 范围: [{sh0.min():.4f}, {sh0.max():.4f}]")
        print(f"  SHN: {shN.shape}, 范围: [{shN.min():.4f}, {shN.max():.4f}]")
        print(f"  透明度: {opacity.shape}, 范围: [{opacity.min():.4f}, {opacity.max():.4f}]")
        print(f"  缩放: {scaling.shape}, 范围: [{scaling.min():.4f}, {scaling.max():.4f}]")
        print(f"  旋转: {rotation.shape}, 范围: [{rotation.min():.4f}, {rotation.max():.4f}]")
        
        # 创建零法向量
        normals = np.zeros_like(xyz)
        
        # 处理球谐系数 - 组合SH0和SHN
        f_dc = sh0.reshape(-1, 3)  # DC分量 [N, 3]
        
        # 处理高阶球谐系数
        if shN.shape[1] > 0:
            f_rest = shN.transpose(0, 2, 1).reshape(-1, shN.shape[1] * 3)  # [N, 9]
        else:
            f_rest = np.zeros((xyz.shape[0], 0))
        
        # 构建PLY属性列表
        attributes = ['x', 'y', 'z', 'nx', 'ny', 'nz']
        attributes.extend(['f_dc_0', 'f_dc_1', 'f_dc_2'])
        
        # 添加高阶球谐系数
        for i in range(f_rest.shape[1]):
            attributes.append(f'f_rest_{i}')
        
        attributes.append('opacity')
        attributes.extend(['scale_0', 'scale_1', 'scale_2'])
        attributes.extend(['rot_0', 'rot_1', 'rot_2', 'rot_3'])
        
        # 创建数据类型
        dtype_list = [(attr, 'f4') for attr in attributes]
        
        # 创建顶点数据
        vertex_data = np.empty(len(xyz), dtype=dtype_list)
        
        # 填充位置和法向量
        vertex_data['x'] = xyz[:, 0]
        vertex_data['y'] = xyz[:, 1] 
        vertex_data['z'] = xyz[:, 2]
        vertex_data['nx'] = normals[:, 0]
        vertex_data['ny'] = normals[:, 1]
        vertex_data['nz'] = normals[:, 2]
        
        # 填充球谐DC分量
        vertex_data['f_dc_0'] = f_dc[:, 0]
        vertex_data['f_dc_1'] = f_dc[:, 1]
        vertex_data['f_dc_2'] = f_dc[:, 2]
        
        # 填充高阶球谐系数
        for i in range(f_rest.shape[1]):
            vertex_data[f'f_rest_{i}'] = f_rest[:, i]
        
        # 填充原始透明度、缩放、旋转
        vertex_data['opacity'] = opacity.flatten()
        vertex_data['scale_0'] = scaling[:, 0]
        vertex_data['scale_1'] = scaling[:, 1]
        vertex_data['scale_2'] = scaling[:, 2]
        vertex_data['rot_0'] = rotation[:, 0]
        vertex_data['rot_1'] = rotation[:, 1]
        vertex_data['rot_2'] = rotation[:, 2]
        vertex_data['rot_3'] = rotation[:, 3]
        
        # 创建PLY文件
        vertex_element = PlyElement.describe(vertex_data, 'vertex')
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        PlyData([vertex_element]).write(output_path)
        
        print(f"保存原始Gaussian PLY: {output_path}")
        print(f"  点数: {len(xyz)}")
        print(f"  球谐阶数: DC + {f_rest.shape[1]//3} 阶")


def save_raw_gaussian_sequence(model_dir, smpl_params_path, output_dir, frame_indices):
    """保存原始Gaussian序列"""
    
    try:
        from scene.gaussian_model import GaussianModel
        from utils.smpl_utils import init_smpl
        from omegaconf import OmegaConf
        
        print(f"加载模型: {model_dir}")
        
        # 加载配置
        config_path = os.path.join(model_dir, 'config.yaml')
        config = OmegaConf.load(config_path)
        
        # 初始化SMPL
        smpl_pkl_path = config.smpl_pkl_path
        if not os.path.isabs(smpl_pkl_path):
            smpl_pkl_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), smpl_pkl_path)
        init_smpl(smpl_pkl_path)
        
        # 加载模型
        gaussians = GaussianModel()
        checkpoint_files = [f for f in os.listdir(model_dir) if f.startswith('chkpnt') and f.endswith('.pth')]
        checkpoint_files.sort(key=lambda x: int(x.replace('chkpnt', '').replace('.pth', '')))
        latest_checkpoint = checkpoint_files[-1]
        checkpoint_path = os.path.join(model_dir, latest_checkpoint)
        
        print(f"加载checkpoint: {latest_checkpoint}")
        checkpoint = torch.load(checkpoint_path, map_location='cuda', weights_only=False)
        gaussians.restore(checkpoint)
        
        print(f"模型加载完成，Gaussian点数: {gaussians._xyz.shape[0]}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存原始状态（不设置任何姿态）
        print("\n保存原始状态（canonical pose）...")
        output_path = os.path.join(output_dir, "raw_canonical.ply")
        save_raw_gaussian_ply(gaussians, output_path)
        
        # 如果需要特定帧，加载SMPL参数
        if frame_indices and len(frame_indices) > 0 and frame_indices[0] >= 0:
            print(f"\n加载SMPL参数: {smpl_params_path}")
            smpl_data = np.load(smpl_params_path, allow_pickle=True)
            
            # 设置测试模式
            gaussians.is_test = True
            gaussians.prepare_test()
            
            # 解析SMPL参数
            poses, transl, expressions, jaw_poses = gaussians._parse_smpl_params(smpl_data)
            
            for frame_idx in frame_indices:
                if frame_idx >= len(poses):
                    print(f"警告: 帧 {frame_idx} 超出范围，跳过")
                    continue
                    
                print(f"\n处理帧 {frame_idx}...")
                
                # 设置帧参数
                gaussians._set_frame_params(
                    poses[frame_idx], transl[frame_idx],
                    expressions[frame_idx] if expressions is not None else None,
                    jaw_poses[frame_idx] if jaw_poses is not None else None
                )
                
                # 保存该帧的原始参数
                output_path = os.path.join(output_dir, f"raw_frame_{frame_idx:06d}.ply")
                save_raw_gaussian_ply(gaussians, output_path)
        
        print(f"\n完成! 所有文件保存在: {output_dir}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


def main():
    parser = ArgumentParser(description="保存原始Gaussian参数")
    parser.add_argument('--model_dir', type=str, required=True, help='模型目录')
    parser.add_argument('--output_dir', type=str, required=True, help='输出目录')
    parser.add_argument('--smpl_params', type=str, help='SMPL参数文件（可选）')
    parser.add_argument('--frames', type=str, default='-1', 
                       help='帧索引，逗号分隔，-1表示只保存canonical pose (默认: -1)')
    
    args = parser.parse_args()
    
    # 解析帧索引
    if args.frames == '-1':
        frame_indices = []
    else:
        frame_indices = [int(x.strip()) for x in args.frames.split(',') if x.strip()]
    
    # 检查参数
    if not os.path.exists(args.model_dir):
        print(f"错误: 模型目录不存在: {args.model_dir}")
        return
    
    if frame_indices and not args.smpl_params:
        print("错误: 指定帧时需要提供 --smpl_params 参数")
        return
    
    if args.smpl_params and not os.path.exists(args.smpl_params):
        print(f"错误: SMPL参数文件不存在: {args.smpl_params}")
        return
    
    # 执行保存
    save_raw_gaussian_sequence(args.model_dir, args.smpl_params, args.output_dir, frame_indices)
    
    print("\n使用说明:")
    print("1. raw_canonical.ply - 原始canonical pose状态")
    print("2. raw_frame_XXXXXX.ply - 特定帧状态")
    print("3. 这些是完全未处理的原始参数，应该能正确显示")


if __name__ == "__main__":
    main()
