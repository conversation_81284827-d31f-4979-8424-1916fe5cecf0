#!/usr/bin/env python3
"""
简单的NPZ到PLY转换脚本
支持多种常见的NPZ格式，自动检测数据结构

用法:
python npz_to_ply_simple.py input.npz output_dir [--start 0] [--end -1] [--step 1] [--color position]
"""

import os
import sys
import numpy as np
from argparse import ArgumentParser

# 添加项目路径以使用utils
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from utils.general_utils import storePly
except ImportError:
    print("Warning: Could not import storePly from utils, using fallback implementation")
    
    def storePly(out_path, xyz, rgb):
        """Fallback PLY writer implementation"""
        try:
            from plyfile import PlyElement, PlyData
        except ImportError:
            raise ImportError("plyfile package is required. Install with: pip install plyfile")

        # Define the dtype for the structured array
        dtype = [('x', 'f4'), ('y', 'f4'), ('z', 'f4'),
                ('nx', 'f4'), ('ny', 'f4'), ('nz', 'f4'),
                ('red', 'u1'), ('green', 'u1'), ('blue', 'u1')]
        
        normals = np.zeros_like(xyz)

        elements = np.empty(xyz.shape[0], dtype=dtype)
        attributes = np.concatenate((xyz, normals, rgb), axis=1)
        elements[:] = list(map(tuple, attributes))

        # Create the PlyData object and write to file
        vertex_element = PlyElement.describe(elements, 'vertex')
        ply_data = PlyData([vertex_element])
        ply_data.write(out_path)


def analyze_npz_structure(npz_file):
    """分析NPZ文件结构"""
    print(f"Analyzing NPZ file: {npz_file}")
    
    data = np.load(npz_file, allow_pickle=True)
    print(f"Available keys: {list(data.keys())}")
    
    for key in data.keys():
        value = data[key]
        if isinstance(value, np.ndarray):
            print(f"  {key}: shape={value.shape}, dtype={value.dtype}")
            if value.ndim <= 2 and value.size < 20:
                print(f"    sample values: {value.flatten()[:10]}")
        else:
            print(f"  {key}: type={type(value)}")
    
    return data


def detect_point_cloud_data(data):
    """自动检测点云数据"""
    # 常见的点云数据键名
    point_keys = ['points', 'vertices', 'xyz', 'positions', 'point_cloud', 'verts', 'mesh_vertices']
    color_keys = ['colors', 'rgb', 'color', 'vertex_colors']
    
    points = None
    colors = None
    
    # 查找点云数据
    for key in point_keys:
        if key in data:
            candidate = data[key]
            if isinstance(candidate, np.ndarray) and candidate.ndim >= 2:
                # 检查最后一维是否为3（xyz坐标）
                if candidate.shape[-1] == 3:
                    points = candidate
                    print(f"Found point cloud data: {key}, shape: {points.shape}")
                    break
    
    # 查找颜色数据
    for key in color_keys:
        if key in data:
            candidate = data[key]
            if isinstance(candidate, np.ndarray) and candidate.ndim >= 2:
                # 检查最后一维是否为3（RGB颜色）
                if candidate.shape[-1] == 3:
                    colors = candidate
                    print(f"Found color data: {key}, shape: {colors.shape}")
                    break
    
    if points is None:
        # 如果没有找到明确的点云数据，尝试查找形状合适的数组
        for key, value in data.items():
            if isinstance(value, np.ndarray) and value.ndim >= 2 and value.shape[-1] == 3:
                if value.size > 100:  # 假设点云至少有100个点
                    points = value
                    print(f"Auto-detected point cloud data: {key}, shape: {points.shape}")
                    break
    
    return points, colors


def generate_colors(xyz, color_mode='position'):
    """生成颜色"""
    if color_mode == 'position':
        # 基于位置的着色
        colors = np.clip((xyz - xyz.min(axis=0)) / (xyz.max(axis=0) - xyz.min(axis=0)) * 255, 0, 255).astype(np.uint8)
    elif color_mode == 'uniform':
        # 统一白色
        colors = np.full((len(xyz), 3), 255, dtype=np.uint8)
    elif color_mode == 'random':
        # 随机颜色
        colors = np.random.randint(0, 256, (len(xyz), 3), dtype=np.uint8)
    elif color_mode == 'height':
        # 基于高度（Y坐标）的着色
        y_normalized = (xyz[:, 1] - xyz[:, 1].min()) / (xyz[:, 1].max() - xyz[:, 1].min())
        colors = np.zeros((len(xyz), 3), dtype=np.uint8)
        colors[:, 0] = (y_normalized * 255).astype(np.uint8)  # Red channel
        colors[:, 2] = ((1 - y_normalized) * 255).astype(np.uint8)  # Blue channel
    else:
        # 默认位置着色
        colors = np.clip((xyz - xyz.min(axis=0)) / (xyz.max(axis=0) - xyz.min(axis=0)) * 255, 0, 255).astype(np.uint8)
    
    return colors


def convert_npz_to_ply(npz_file, output_dir, frame_start=0, frame_end=None, frame_step=1, color_mode='position'):
    """转换NPZ文件到PLY序列"""
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 分析NPZ文件
    data = analyze_npz_structure(npz_file)
    
    # 检测点云数据
    points, colors = detect_point_cloud_data(data)
    
    if points is None:
        raise ValueError("No point cloud data found in NPZ file")
    
    # 处理数据维度
    if points.ndim == 2:
        # 单帧数据 [N_points, 3]
        points = points[np.newaxis, ...]  # 转为 [1, N_points, 3]
        if colors is not None and colors.ndim == 2:
            colors = colors[np.newaxis, ...]
        print(f"Single frame detected, converted to shape: {points.shape}")
    elif points.ndim == 3:
        # 多帧数据 [N_frames, N_points, 3]
        print(f"Multi-frame data detected: {points.shape}")
    else:
        raise ValueError(f"Unsupported point cloud shape: {points.shape}")
    
    # 确定帧范围
    total_frames = points.shape[0]
    if frame_end is None or frame_end < 0:
        frame_end = total_frames
    frame_end = min(frame_end, total_frames)
    
    print(f"Converting frames {frame_start} to {frame_end-1}, step {frame_step}")
    print(f"Total {len(range(frame_start, frame_end, frame_step))} PLY files to generate")
    
    # 转换每一帧
    for frame_idx in range(frame_start, frame_end, frame_step):
        xyz = points[frame_idx]  # [N_points, 3]
        
        # 处理颜色
        if colors is not None:
            if colors.ndim == 3:  # [N_frames, N_points, 3]
                frame_colors = colors[frame_idx]
            else:  # [N_points, 3]
                frame_colors = colors
            
            # 确保颜色值在0-255范围内
            if frame_colors.max() <= 1.0:
                frame_colors = (frame_colors * 255).astype(np.uint8)
            else:
                frame_colors = np.clip(frame_colors, 0, 255).astype(np.uint8)
        else:
            # 生成颜色
            frame_colors = generate_colors(xyz, color_mode)
        
        # 保存PLY文件
        ply_path = os.path.join(output_dir, f"frame_{frame_idx:06d}.ply")
        storePly(ply_path, xyz, frame_colors)
        
        if frame_idx % 50 == 0 or frame_idx == frame_start:
            print(f"Converted frame {frame_idx}, points: {len(xyz)}")
    
    print(f"Conversion complete! PLY files saved to: {output_dir}")


def main():
    parser = ArgumentParser(description="Convert NPZ files to PLY sequence")
    parser.add_argument('npz_file', type=str, help='Input NPZ file path')
    parser.add_argument('output_dir', type=str, help='Output directory for PLY files')
    parser.add_argument('--start', type=int, default=0, help='Start frame (default: 0)')
    parser.add_argument('--end', type=int, default=None, help='End frame (default: all frames)')
    parser.add_argument('--step', type=int, default=1, help='Frame step (default: 1)')
    parser.add_argument('--color', type=str, default='position', 
                       choices=['position', 'uniform', 'random', 'height'],
                       help='Color mode (default: position)')
    parser.add_argument('--analyze-only', action='store_true', 
                       help='Only analyze NPZ structure without conversion')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.npz_file):
        print(f"Error: NPZ file does not exist: {args.npz_file}")
        return
    
    try:
        if args.analyze_only:
            # 只分析文件结构
            analyze_npz_structure(args.npz_file)
        else:
            # 执行转换
            convert_npz_to_ply(
                npz_file=args.npz_file,
                output_dir=args.output_dir,
                frame_start=args.start,
                frame_end=args.end,
                frame_step=args.step,
                color_mode=args.color
            )
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
