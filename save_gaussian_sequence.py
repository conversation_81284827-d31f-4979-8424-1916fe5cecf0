#!/usr/bin/env python3
"""
保存Gaussian Splatting模型的点云序列为PLY文件
用法: python save_gaussian_sequence.py --model_dir MODEL_DIR --smpl_params SMPL_PARAMS.npz --output_dir OUTPUT_DIR [options]
"""

import os
import sys
import torch
import numpy as np
from argparse import ArgumentParser

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scene.gaussian_model import GaussianModel
from utils.config_utils import Config
from utils.general_utils import storePly


def load_model(model_dir):
    """加载训练好的Gaussian模型"""
    print(f"Loading model from: {model_dir}")
    
    # 加载配置
    config_path = os.path.join(model_dir, 'config.yaml')
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Config file not found: {config_path}")
    
    config = Config(config_path)
    
    # 查找最新的checkpoint
    checkpoint_files = [f for f in os.listdir(model_dir) if f.startswith('chkpnt') and f.endswith('.pth')]
    if not checkpoint_files:
        raise FileNotFoundError(f"No checkpoint files found in {model_dir}")
    
    # 按数字排序，取最新的
    checkpoint_files.sort(key=lambda x: int(x.replace('chkpnt', '').replace('.pth', '')))
    latest_checkpoint = checkpoint_files[-1]
    checkpoint_path = os.path.join(model_dir, latest_checkpoint)
    
    print(f"Loading checkpoint: {latest_checkpoint}")
    
    # 创建模型并加载
    gaussians = GaussianModel()
    checkpoint = torch.load(checkpoint_path, map_location='cuda')
    gaussians.restore(checkpoint)
    
    print(f"Model loaded successfully. Number of Gaussian points: {gaussians._xyz.shape[0]}")
    return gaussians


def save_gaussian_sequence(gaussians, smpl_params_path, output_dir, frame_start=0, frame_end=None, frame_step=1, color_mode='position'):
    """
    保存Gaussian点的全局坐标序列为PLY文件
    
    Args:
        gaussians: GaussianModel实例
        smpl_params_path: SMPL参数文件路径 (.npz)
        output_dir: 输出目录
        frame_start: 起始帧 (默认0)
        frame_end: 结束帧 (默认None表示到最后一帧)
        frame_step: 帧步长 (默认1)
        color_mode: 着色模式 ('position', 'opacity', 'sh', 'uniform')
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载SMPL参数
    print(f"Loading SMPL parameters from: {smpl_params_path}")
    smpl_data = np.load(smpl_params_path, allow_pickle=True)
    poses = smpl_data['pose']
    transl = smpl_data.get('transl', np.zeros((len(poses), 3)))
    
    # 检查是否有表情和下颌参数
    if 'expression' in smpl_data:
        expressions = smpl_data['expression']
        print(f"Found expression parameters: {expressions.shape}")
    else:
        expressions = np.zeros((len(poses), 10))
        
    if 'jaw_pose' in smpl_data:
        jaw_poses = smpl_data['jaw_pose']
        print(f"Found jaw pose parameters: {jaw_poses.shape}")
    else:
        jaw_poses = np.zeros((len(poses), 3))
    
    if 'Rh' in smpl_data:
        global_rots = smpl_data['Rh']
    else:
        global_rots = poses[:, :3]
    
    # 确定帧范围
    total_frames = len(poses)
    if frame_end is None:
        frame_end = total_frames
    frame_end = min(frame_end, total_frames)
    
    print(f"开始保存Gaussian点序列 (着色模式: {color_mode})")
    print(f"帧范围: {frame_start} 到 {frame_end-1}, 步长: {frame_step}")
    print(f"总共 {len(range(frame_start, frame_end, frame_step))} 个PLY文件")
    
    for frame_idx in range(frame_start, frame_end, frame_step):
        # 设置当前帧的姿态参数
        current_pose = torch.from_numpy(poses[frame_idx]).float()
        current_transl = torch.from_numpy(transl[frame_idx]).float()
        current_rot = torch.from_numpy(global_rots[frame_idx]).float()
        current_expression = torch.from_numpy(expressions[frame_idx]).float()
        current_jaw_pose = torch.from_numpy(jaw_poses[frame_idx]).float()
        
        # 更新模型参数
        gaussians.smpl_poses = current_pose
        gaussians.Th = current_transl
        gaussians.Rh = current_rot
        gaussians.expression = current_expression
        gaussians.jaw_pose = current_jaw_pose
        
        # 获取当前帧的数据
        with torch.no_grad():
            xyz_global = gaussians.get_xyz.cpu().numpy()
            
            # 根据着色模式生成颜色
            if color_mode == 'position':
                # 基于位置的着色
                colors = np.clip((xyz_global - xyz_global.min()) / (xyz_global.max() - xyz_global.min()) * 255, 0, 255).astype(np.uint8)
            elif color_mode == 'opacity':
                # 基于透明度的着色
                opacity = gaussians.get_opacity.cpu().numpy()
                opacity_normalized = (opacity - opacity.min()) / (opacity.max() - opacity.min())
                colors = np.stack([opacity_normalized * 255, opacity_normalized * 255, opacity_normalized * 255], axis=1).astype(np.uint8)
            elif color_mode == 'sh':
                # 基于球谐函数的着色
                sh = gaussians.get_sh.cpu().numpy()
                if sh.shape[1] > 0:
                    sh_colors = sh[:, 0, :]  # 取第0阶球谐系数
                    sh_colors = np.clip((sh_colors + 0.5) * 255, 0, 255).astype(np.uint8)
                    colors = sh_colors
                else:
                    colors = np.full((len(xyz_global), 3), 128, dtype=np.uint8)
            elif color_mode == 'uniform':
                # 统一颜色 (白色)
                colors = np.full((len(xyz_global), 3), 255, dtype=np.uint8)
            else:
                # 默认使用位置着色
                colors = np.clip((xyz_global - xyz_global.min()) / (xyz_global.max() - xyz_global.min()) * 255, 0, 255).astype(np.uint8)
            
            # 保存PLY文件
            ply_path = os.path.join(output_dir, f"gaussian_frame_{frame_idx:06d}.ply")
            storePly(ply_path, xyz_global, colors)
            
        if frame_idx % 10 == 0:
            print(f"已保存帧 {frame_idx}/{frame_end-1}")
    
    print(f"完成! 所有PLY文件已保存到: {output_dir}")


def main():
    parser = ArgumentParser(description="保存Gaussian Splatting模型的点云序列为PLY文件")
    parser.add_argument('--model_dir', type=str, required=True, help='模型目录路径')
    parser.add_argument('--smpl_params', type=str, required=True, help='SMPL参数文件路径 (.npz)')
    parser.add_argument('--output_dir', type=str, required=True, help='输出目录')
    parser.add_argument('--frame_start', type=int, default=0, help='起始帧 (默认: 0)')
    parser.add_argument('--frame_end', type=int, default=None, help='结束帧 (默认: 到最后一帧)')
    parser.add_argument('--frame_step', type=int, default=1, help='帧步长 (默认: 1)')
    parser.add_argument('--color_mode', type=str, default='position', 
                       choices=['position', 'opacity', 'sh', 'uniform'],
                       help='着色模式 (默认: position)')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.model_dir):
        print(f"错误: 模型目录不存在: {args.model_dir}")
        return
        
    if not os.path.exists(args.smpl_params):
        print(f"错误: SMPL参数文件不存在: {args.smpl_params}")
        return
    
    try:
        # 加载模型
        gaussians = load_model(args.model_dir)
        
        # 保存序列
        save_gaussian_sequence(
            gaussians=gaussians,
            smpl_params_path=args.smpl_params,
            output_dir=args.output_dir,
            frame_start=args.frame_start,
            frame_end=args.frame_end,
            frame_step=args.frame_step,
            color_mode=args.color_mode
        )
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
