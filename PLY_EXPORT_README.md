# PLY导出功能说明

本项目现在支持将Gaussian模型和NPZ文件导出为PLY格式的点云序列。

## 功能概述

### 1. GaussianModel内置PLY保存功能

在`scene/gaussian_model.py`中添加了以下方法：

- `save_gaussian_sequence_to_ply()`: 保存Gaussian点序列为PLY文件
- `save_current_frame_to_ply()`: 保存当前帧为单个PLY文件
- 支持标准Gaussian Splatting格式和简化点云格式

### 2. 独立导出脚本

提供了两个独立的Python脚本：

- `export_npz_to_ply.py`: 通用导出脚本，支持从Gaussian模型或直接从NPZ导出
- `npz_to_ply_simple.py`: 简化的NPZ转PLY脚本，自动检测数据格式

## 使用方法

### 方法1: 使用GaussianModel内置功能

```python
from scene.gaussian_model import GaussianModel

# 加载模型
gaussians = GaussianModel()
gaussians.restore(checkpoint)
gaussians.is_test = True
gaussians.prepare_test()

# 保存PLY序列
gaussians.save_gaussian_sequence_to_ply(
    smpl_params_path="path/to/smpl_params.npz",
    output_dir="output/ply_sequence",
    frame_start=0,
    frame_end=100,
    frame_step=1,
    format_type='simple'  # 或 'standard'
)

# 保存单帧
gaussians.save_current_frame_to_ply(
    output_path="output/current_frame.ply",
    format_type='simple',
    color_mode='sh'
)
```

### 方法2: 使用通用导出脚本

```bash
# 从Gaussian模型导出
python export_npz_to_ply.py \
    --mode gaussian \
    --model_dir path/to/model \
    --smpl_params path/to/smpl_params.npz \
    --output_dir output/ply_sequence \
    --format_type simple \
    --color_mode sh

# 直接从NPZ文件导出
python export_npz_to_ply.py \
    --mode direct \
    --npz_file path/to/pointcloud.npz \
    --output_dir output/ply_sequence \
    --color_mode position
```

### 方法3: 使用简化NPZ转换脚本

```bash
# 基本用法
python npz_to_ply_simple.py input.npz output_dir

# 指定帧范围和着色模式
python npz_to_ply_simple.py input.npz output_dir \
    --start 0 --end 100 --step 2 --color height

# 只分析NPZ文件结构
python npz_to_ply_simple.py input.npz output_dir --analyze-only
```

## 参数说明

### PLY格式类型

- **standard**: 标准Gaussian Splatting格式，包含完整的Gaussian属性（位置、旋转、缩放、透明度、球谐系数等）
- **simple**: 简化点云格式，只包含位置和颜色信息

### 着色模式

- **position**: 基于3D位置的彩虹着色
- **opacity**: 基于透明度的灰度着色
- **sh**: 基于球谐函数的真实颜色（仅Gaussian模式）
- **uniform**: 统一白色
- **random**: 随机颜色
- **height**: 基于高度的红蓝渐变

## 支持的NPZ格式

脚本能自动检测以下NPZ文件格式：

### 点云数据键名
- `points`, `vertices`, `xyz`, `positions`, `point_cloud`, `verts`, `mesh_vertices`

### 颜色数据键名
- `colors`, `rgb`, `color`, `vertex_colors`

### SMPL参数格式
- 完整格式: `pose` (165维)
- 分离格式: `global_orient` + `body_pose` + `left_hand_pose` + `right_hand_pose`
- 平移: `transl` 或 `Th`
- 表情: `expression` (使用前10维)
- 下颌: `jaw_pose`

## 数据格式要求

### 点云数据
- 单帧: `[N_points, 3]`
- 多帧: `[N_frames, N_points, 3]`

### 颜色数据（可选）
- 单帧: `[N_points, 3]`
- 多帧: `[N_frames, N_points, 3]` 或 `[N_points, 3]`
- 值范围: 0-1 (浮点) 或 0-255 (整数)

## 输出文件命名

- Gaussian模式: `gaussian_frame_XXXXXX.ply`
- Direct模式: `frame_XXXXXX.ply`

其中XXXXXX是6位数的帧编号。

## 依赖包

确保安装以下Python包：

```bash
pip install plyfile numpy torch omegaconf
```

## 示例用法

### 示例1: 从训练好的模型导出动画序列

```bash
python export_npz_to_ply.py \
    --mode gaussian \
    --model_dir experiments/person1/model \
    --smpl_params data/person1_sequence.npz \
    --output_dir output/person1_ply \
    --frame_start 0 \
    --frame_end 200 \
    --frame_step 2 \
    --format_type simple \
    --color_mode sh
```

### 示例2: 从点云NPZ文件导出

```bash
python npz_to_ply_simple.py \
    pointcloud_sequence.npz \
    output/pointcloud_ply \
    --start 10 \
    --end 50 \
    --step 1 \
    --color position
```

### 示例3: 分析未知NPZ文件格式

```bash
python npz_to_ply_simple.py unknown_file.npz output --analyze-only
```

## 注意事项

1. **内存使用**: 大型点云序列可能占用大量内存，建议分批处理
2. **文件大小**: 标准格式的PLY文件比简化格式大很多
3. **坐标系**: 输出的PLY文件保持原始坐标系
4. **兼容性**: PLY文件可以在MeshLab、CloudCompare、Blender等软件中查看

## 故障排除

### 常见错误

1. **"No point cloud data found"**: NPZ文件中没有识别的点云数据键名
2. **"plyfile package required"**: 需要安装plyfile包
3. **"SMPL model not initialized"**: 需要正确设置SMPL模型路径

### 解决方案

1. 使用`--analyze-only`参数检查NPZ文件结构
2. 确保所有依赖包已正确安装
3. 检查文件路径和权限
4. 对于大文件，尝试减少帧范围或增加步长
