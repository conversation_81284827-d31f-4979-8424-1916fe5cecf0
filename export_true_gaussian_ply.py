#!/usr/bin/env python3
"""
导出真正正确的Gaussian Splatting PLY文件
使用原始的未激活参数，符合标准3DGS格式
"""

import os
import sys
import torch
import numpy as np
from argparse import ArgumentParser

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from plyfile import PlyData, PlyElement
except ImportError:
    print("Error: plyfile package is required. Install with: pip install plyfile")
    sys.exit(1)


def save_true_gaussian_ply(gaussians, output_path, cam_pos=None):
    """
    保存真正正确的Gaussian Splatting PLY文件
    关键：使用原始未激活的参数值
    """
    
    with torch.no_grad():
        # 1. 位置：使用全局变换后的位置
        xyz = gaussians.get_xyz.detach().cpu().numpy()
        
        # 2. 透明度：使用原始logit值（未经sigmoid激活）
        # 这是关键！标准3DGS PLY格式存储的是logit值，不是sigmoid后的值
        if not gaussians.is_gsparam_bs:
            raw_opacity = gaussians._opacity.detach().cpu().numpy()
        else:
            features = gaussians.get_encoded_feature_gsparam_weight
            dopacity = torch.einsum('nc,nc->n', features, gaussians.opacity_bs)
            raw_opacity = (gaussians._opacity + dopacity).detach().cpu().numpy()
        
        # 3. 缩放：使用原始log值（未经exp激活）
        # 这也是关键！标准3DGS PLY格式存储的是log值，不是exp后的值
        if not gaussians.is_gsparam_bs:
            raw_scaling = gaussians._scaling.detach().cpu().numpy()
        else:
            features = gaussians.get_encoded_feature_gsparam_weight
            dscaling = torch.einsum('nc,ncl->nl', features, gaussians.scaling_bs)
            raw_scaling = (gaussians._scaling + dscaling).detach().cpu().numpy()
        
        # 4. 旋转：使用原始四元数（未经归一化）
        if not gaussians.is_gsparam_bs:
            raw_rotation = gaussians._rotation.detach().cpu().numpy()
        else:
            features = gaussians.get_encoded_feature_gsparam_weight
            drotation = torch.einsum('nc,ncl->nl', features, gaussians.rotation_bs)
            raw_rotation = (gaussians._rotation + drotation).detach().cpu().numpy()
        
        # 5. 球谐函数：使用原始SH系数
        sh0 = gaussians._sh0.detach().cpu().numpy()  # [N, 1, 3]
        shN = gaussians._shN.detach().cpu().numpy()  # [N, 3, 3]
        
        # 处理球谐系数格式
        f_dc = sh0.reshape(-1, 3)  # DC分量 [N, 3]
        if shN.shape[1] > 0:
            f_rest = shN.transpose(0, 2, 1).reshape(-1, shN.shape[1] * 3)  # [N, 9]
        else:
            f_rest = np.zeros((xyz.shape[0], 0))
        
        print(f"真正的Gaussian参数统计:")
        print(f"  位置: {xyz.shape}, 范围: [{xyz.min():.4f}, {xyz.max():.4f}]")
        print(f"  原始透明度(logit): {raw_opacity.shape}, 范围: [{raw_opacity.min():.4f}, {raw_opacity.max():.4f}]")
        print(f"  原始缩放(log): {raw_scaling.shape}, 范围: [{raw_scaling.min():.4f}, {raw_scaling.max():.4f}]")
        print(f"  原始旋转: {raw_rotation.shape}, 范围: [{raw_rotation.min():.4f}, {raw_rotation.max():.4f}]")
        print(f"  SH DC: {f_dc.shape}, 范围: [{f_dc.min():.4f}, {f_dc.max():.4f}]")
        print(f"  SH rest: {f_rest.shape}")
        
        # 验证激活后的值
        activated_opacity = torch.sigmoid(torch.from_numpy(raw_opacity)).numpy()
        activated_scaling = torch.exp(torch.from_numpy(raw_scaling)).numpy()
        print(f"  验证 - 激活后透明度范围: [{activated_opacity.min():.4f}, {activated_opacity.max():.4f}]")
        print(f"  验证 - 激活后缩放范围: [{activated_scaling.min():.6f}, {activated_scaling.max():.6f}]")
        
        # 创建零法向量
        normals = np.zeros_like(xyz)
        
        # 构建PLY属性列表
        attributes = ['x', 'y', 'z', 'nx', 'ny', 'nz']
        attributes.extend(['f_dc_0', 'f_dc_1', 'f_dc_2'])
        
        # 添加高阶球谐系数
        for i in range(f_rest.shape[1]):
            attributes.append(f'f_rest_{i}')
        
        attributes.append('opacity')
        attributes.extend(['scale_0', 'scale_1', 'scale_2'])
        attributes.extend(['rot_0', 'rot_1', 'rot_2', 'rot_3'])
        
        # 创建数据类型
        dtype_list = [(attr, 'f4') for attr in attributes]
        
        # 创建顶点数据
        vertex_data = np.empty(len(xyz), dtype=dtype_list)
        
        # 填充数据
        vertex_data['x'] = xyz[:, 0]
        vertex_data['y'] = xyz[:, 1]
        vertex_data['z'] = xyz[:, 2]
        vertex_data['nx'] = normals[:, 0]
        vertex_data['ny'] = normals[:, 1]
        vertex_data['nz'] = normals[:, 2]
        
        # 填充球谐DC分量
        vertex_data['f_dc_0'] = f_dc[:, 0]
        vertex_data['f_dc_1'] = f_dc[:, 1]
        vertex_data['f_dc_2'] = f_dc[:, 2]
        
        # 填充高阶球谐系数
        for i in range(f_rest.shape[1]):
            vertex_data[f'f_rest_{i}'] = f_rest[:, i]
        
        # 填充原始参数（这是关键！）
        vertex_data['opacity'] = raw_opacity.flatten()
        vertex_data['scale_0'] = raw_scaling[:, 0]
        vertex_data['scale_1'] = raw_scaling[:, 1]
        vertex_data['scale_2'] = raw_scaling[:, 2]
        vertex_data['rot_0'] = raw_rotation[:, 0]
        vertex_data['rot_1'] = raw_rotation[:, 1]
        vertex_data['rot_2'] = raw_rotation[:, 2]
        vertex_data['rot_3'] = raw_rotation[:, 3]
        
        # 保存PLY文件
        vertex_element = PlyElement.describe(vertex_data, 'vertex')
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        PlyData([vertex_element]).write(output_path)
        
        print(f"保存真正的Gaussian PLY: {output_path}")


def main():
    parser = ArgumentParser(description="导出真正正确的Gaussian Splatting PLY文件")
    parser.add_argument('--model_dir', type=str, required=True, help='模型目录')
    parser.add_argument('--output_dir', type=str, required=True, help='输出目录')
    parser.add_argument('--smpl_params', type=str, help='SMPL参数文件')
    parser.add_argument('--frames', type=str, default='0', help='帧索引，逗号分隔 (默认: 0)')
    
    args = parser.parse_args()
    
    # 解析参数
    frame_indices = [int(x.strip()) for x in args.frames.split(',')]
    
    try:
        from scene.gaussian_model import GaussianModel
        from utils.smpl_utils import init_smpl
        from omegaconf import OmegaConf
        
        print(f"加载模型: {args.model_dir}")
        
        # 加载配置和模型
        config_path = os.path.join(args.model_dir, 'config.yaml')
        config = OmegaConf.load(config_path)
        
        smpl_pkl_path = config.smpl_pkl_path
        if not os.path.isabs(smpl_pkl_path):
            smpl_pkl_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), smpl_pkl_path)
        init_smpl(smpl_pkl_path)
        
        gaussians = GaussianModel()
        checkpoint_files = [f for f in os.listdir(args.model_dir) if f.startswith('chkpnt') and f.endswith('.pth')]
        checkpoint_files.sort(key=lambda x: int(x.replace('chkpnt', '').replace('.pth', '')))
        latest_checkpoint = checkpoint_files[-1]
        checkpoint_path = os.path.join(args.model_dir, latest_checkpoint)
        
        print(f"加载checkpoint: {latest_checkpoint}")
        checkpoint = torch.load(checkpoint_path, map_location='cuda', weights_only=False)
        gaussians.restore(checkpoint)
        gaussians.is_test = True
        gaussians.prepare_test()
        
        os.makedirs(args.output_dir, exist_ok=True)
        
        if args.smpl_params:
            # 加载SMPL参数并导出特定帧
            smpl_data = np.load(args.smpl_params, allow_pickle=True)
            poses, transl, expressions, jaw_poses = gaussians._parse_smpl_params(smpl_data)
            
            for frame_idx in frame_indices:
                print(f"\n处理帧 {frame_idx}...")
                gaussians._set_frame_params(
                    poses[frame_idx], transl[frame_idx],
                    expressions[frame_idx] if expressions is not None else None,
                    jaw_poses[frame_idx] if jaw_poses is not None else None
                )
                
                output_path = os.path.join(args.output_dir, f"true_gaussian_frame_{frame_idx:06d}.ply")
                save_true_gaussian_ply(gaussians, output_path)
        else:
            # 只导出canonical pose
            output_path = os.path.join(args.output_dir, "true_gaussian_canonical.ply")
            save_true_gaussian_ply(gaussians, output_path)
        
        print(f"\n完成! 文件保存在: {args.output_dir}")
        print("\n关键修复:")
        print("✓ 使用原始logit透明度值（未经sigmoid激活）")
        print("✓ 使用原始log缩放值（未经exp激活）")
        print("✓ 使用原始四元数值（未经归一化）")
        print("✓ 使用全局变换后的位置")
        print("✓ 使用原始球谐系数")
        print("\n这些PLY文件应该能在标准3DGS查看器中正确显示！")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
