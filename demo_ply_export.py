#!/usr/bin/env python3
"""
PLY导出功能演示脚本
创建示例数据并演示各种导出功能
"""

import os
import sys
import numpy as np
import tempfile

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def create_demo_pointcloud_sequence():
    """创建演示用的点云序列"""
    print("创建演示点云序列...")
    
    n_frames = 20
    n_points = 1000
    
    points_sequence = []
    colors_sequence = []
    
    for frame in range(n_frames):
        # 创建旋转的螺旋形点云
        t = frame / n_frames * 4 * np.pi
        
        # 生成螺旋形状
        theta = np.linspace(0, 4*np.pi, n_points)
        r = np.linspace(0.1, 1.0, n_points)
        
        x = r * np.cos(theta + t) * 0.5
        y = r * np.sin(theta + t) * 0.5
        z = np.linspace(-1, 1, n_points) + 0.2 * np.sin(theta + t)
        
        points = np.stack([x, y, z], axis=1)
        
        # 基于位置和时间的颜色
        colors = np.zeros((n_points, 3))
        colors[:, 0] = (np.sin(theta + t) + 1) / 2  # Red
        colors[:, 1] = (np.cos(theta + t) + 1) / 2  # Green  
        colors[:, 2] = (z + 1) / 2                  # Blue
        
        points_sequence.append(points)
        colors_sequence.append(colors)
    
    return {
        'points': np.array(points_sequence, dtype=np.float32),
        'colors': np.array(colors_sequence, dtype=np.float32)
    }


def create_demo_smpl_sequence():
    """创建演示用的SMPL参数序列"""
    print("创建演示SMPL参数序列...")
    
    n_frames = 15
    
    # 创建简单的动作序列（挥手动作）
    global_orient = np.zeros((n_frames, 3))
    body_pose = np.zeros((n_frames, 63))
    
    # 添加一些简单的动作
    for frame in range(n_frames):
        t = frame / n_frames * 2 * np.pi
        
        # 轻微的身体摆动
        global_orient[frame, 1] = 0.1 * np.sin(t)  # Y轴旋转
        
        # 手臂挥动（简化的关节角度）
        body_pose[frame, 16] = 0.5 * np.sin(t)     # 右肩
        body_pose[frame, 17] = 0.3 * np.cos(t)     # 右肩
        body_pose[frame, 19] = 0.8 * np.sin(t)     # 右肘
    
    # 其他参数
    left_hand_pose = np.zeros((n_frames, 45))
    right_hand_pose = np.zeros((n_frames, 45))
    transl = np.zeros((n_frames, 3))
    expression = np.random.randn(n_frames, 50) * 0.05
    jaw_pose = np.zeros((n_frames, 3))
    
    return {
        'global_orient': global_orient.astype(np.float32),
        'body_pose': body_pose.astype(np.float32),
        'left_hand_pose': left_hand_pose.astype(np.float32),
        'right_hand_pose': right_hand_pose.astype(np.float32),
        'transl': transl.astype(np.float32),
        'expression': expression.astype(np.float32),
        'jaw_pose': jaw_pose.astype(np.float32)
    }


def demo_simple_conversion():
    """演示简单的NPZ到PLY转换"""
    print("\n=== 演示1: 简单NPZ到PLY转换 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建演示数据
        demo_data = create_demo_pointcloud_sequence()
        npz_file = os.path.join(temp_dir, "demo_pointcloud.npz")
        output_dir = os.path.join(temp_dir, "ply_output")
        
        # 保存NPZ文件
        np.savez(npz_file, **demo_data)
        print(f"保存演示数据到: {npz_file}")
        
        # 转换为PLY
        try:
            from npz_to_ply_simple import convert_npz_to_ply
            
            print("转换前5帧，使用位置着色...")
            convert_npz_to_ply(
                npz_file=npz_file,
                output_dir=output_dir,
                frame_start=0,
                frame_end=5,
                frame_step=1,
                color_mode='position'
            )
            
            # 检查输出
            ply_files = [f for f in os.listdir(output_dir) if f.endswith('.ply')]
            print(f"✓ 成功生成 {len(ply_files)} 个PLY文件")
            print(f"文件列表: {sorted(ply_files)}")
            
            # 显示文件大小
            for ply_file in sorted(ply_files)[:3]:  # 只显示前3个
                file_path = os.path.join(output_dir, ply_file)
                size_kb = os.path.getsize(file_path) / 1024
                print(f"  {ply_file}: {size_kb:.1f} KB")
                
        except Exception as e:
            print(f"✗ 转换失败: {e}")


def demo_different_color_modes():
    """演示不同的着色模式"""
    print("\n=== 演示2: 不同着色模式 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建简单的单帧数据
        n_points = 500
        # 创建一个球形点云
        theta = np.random.rand(n_points) * 2 * np.pi
        phi = np.random.rand(n_points) * np.pi
        r = np.random.rand(n_points) * 0.5 + 0.5
        
        x = r * np.sin(phi) * np.cos(theta)
        y = r * np.sin(phi) * np.sin(theta)
        z = r * np.cos(phi)
        
        points = np.stack([x, y, z], axis=1)
        
        demo_data = {'points': points.astype(np.float32)}
        npz_file = os.path.join(temp_dir, "sphere.npz")
        np.savez(npz_file, **demo_data)
        
        # 测试不同的着色模式
        color_modes = ['position', 'uniform', 'random', 'height']
        
        try:
            from npz_to_ply_simple import convert_npz_to_ply
            
            for color_mode in color_modes:
                output_dir = os.path.join(temp_dir, f"output_{color_mode}")
                print(f"测试着色模式: {color_mode}")
                
                convert_npz_to_ply(
                    npz_file=npz_file,
                    output_dir=output_dir,
                    color_mode=color_mode
                )
                
                ply_files = [f for f in os.listdir(output_dir) if f.endswith('.ply')]
                print(f"  ✓ 生成文件: {ply_files[0]}")
                
        except Exception as e:
            print(f"✗ 着色模式测试失败: {e}")


def demo_file_analysis():
    """演示文件分析功能"""
    print("\n=== 演示3: NPZ文件分析 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建不同格式的测试文件
        test_files = {}
        
        # 格式1: 标准点云
        test_files['standard_pointcloud.npz'] = {
            'vertices': np.random.rand(100, 3).astype(np.float32),
            'colors': np.random.rand(100, 3).astype(np.float32)
        }
        
        # 格式2: 多帧序列
        test_files['sequence.npz'] = {
            'points': np.random.rand(5, 200, 3).astype(np.float32),
            'rgb': np.random.rand(5, 200, 3).astype(np.float32)
        }
        
        # 格式3: SMPL参数
        test_files['smpl_params.npz'] = create_demo_smpl_sequence()
        
        # 格式4: 混合数据
        test_files['mixed_data.npz'] = {
            'mesh_vertices': np.random.rand(300, 3).astype(np.float32),
            'vertex_colors': np.random.rand(300, 3).astype(np.float32),
            'faces': np.random.randint(0, 300, (100, 3)),
            'metadata': {'source': 'demo', 'version': 1.0}
        }
        
        try:
            from npz_to_ply_simple import analyze_npz_structure
            
            for filename, data in test_files.items():
                file_path = os.path.join(temp_dir, filename)
                np.savez(file_path, **data)
                
                print(f"\n--- 分析文件: {filename} ---")
                analyze_npz_structure(file_path)
                
        except Exception as e:
            print(f"✗ 文件分析失败: {e}")


def demo_command_line_usage():
    """演示命令行使用方法"""
    print("\n=== 演示4: 命令行使用示例 ===")
    
    print("以下是一些常用的命令行使用示例：")
    print()
    
    print("1. 基本转换:")
    print("   python npz_to_ply_simple.py input.npz output_dir")
    print()
    
    print("2. 指定帧范围:")
    print("   python npz_to_ply_simple.py input.npz output_dir --start 10 --end 50 --step 2")
    print()
    
    print("3. 不同着色模式:")
    print("   python npz_to_ply_simple.py input.npz output_dir --color height")
    print()
    
    print("4. 只分析文件结构:")
    print("   python npz_to_ply_simple.py input.npz output_dir --analyze-only")
    print()
    
    print("5. 从Gaussian模型导出:")
    print("   python export_npz_to_ply.py --mode gaussian \\")
    print("       --model_dir path/to/model \\")
    print("       --smpl_params path/to/params.npz \\")
    print("       --output_dir output/ply")
    print()
    
    print("6. 直接从NPZ导出:")
    print("   python export_npz_to_ply.py --mode direct \\")
    print("       --npz_file input.npz \\")
    print("       --output_dir output/ply")


def main():
    """运行所有演示"""
    print("PLY导出功能演示")
    print("=" * 50)
    
    try:
        demo_simple_conversion()
        demo_different_color_modes()
        demo_file_analysis()
        demo_command_line_usage()
        
        print("\n" + "=" * 50)
        print("演示完成！")
        print("\n主要功能:")
        print("✓ NPZ文件自动格式检测")
        print("✓ 多种着色模式支持")
        print("✓ 单帧和多帧数据处理")
        print("✓ 文件结构分析")
        print("✓ 命令行和编程接口")
        
        print("\n下一步:")
        print("1. 查看 PLY_EXPORT_README.md 了解详细使用说明")
        print("2. 使用 npz_to_ply_simple.py 处理您的NPZ文件")
        print("3. 如果有Gaussian模型，使用 export_npz_to_ply.py")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
