#!/usr/bin/env python3
"""
测试PLY导出功能的脚本
创建示例数据并测试各种导出功能
"""

import os
import sys
import numpy as np
import tempfile
import shutil

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from utils.general_utils import storePly
except ImportError:
    print("Warning: Could not import storePly, using fallback")
    def storePly(out_path, xyz, rgb):
        print(f"Fallback: Would save PLY to {out_path} with {len(xyz)} points")


def create_test_npz_single_frame():
    """创建单帧测试NPZ文件"""
    # 创建一个简单的立方体点云
    n_points = 1000
    points = np.random.rand(n_points, 3) * 2 - 1  # [-1, 1]范围内的随机点
    colors = np.random.rand(n_points, 3)  # [0, 1]范围内的随机颜色
    
    return {
        'points': points.astype(np.float32),
        'colors': colors.astype(np.float32)
    }


def create_test_npz_multi_frame():
    """创建多帧测试NPZ文件"""
    n_frames = 10
    n_points = 500
    
    points_sequence = []
    colors_sequence = []
    
    for frame in range(n_frames):
        # 创建旋转的点云
        t = frame / n_frames * 2 * np.pi
        
        # 基础点云（球形）
        theta = np.random.rand(n_points) * 2 * np.pi
        phi = np.random.rand(n_points) * np.pi
        r = np.random.rand(n_points) * 0.5 + 0.5
        
        x = r * np.sin(phi) * np.cos(theta + t)
        y = r * np.sin(phi) * np.sin(theta + t)
        z = r * np.cos(phi)
        
        points = np.stack([x, y, z], axis=1)
        
        # 基于位置的颜色
        colors = (points + 1) / 2  # 归一化到[0,1]
        
        points_sequence.append(points)
        colors_sequence.append(colors)
    
    return {
        'vertices': np.array(points_sequence, dtype=np.float32),
        'vertex_colors': np.array(colors_sequence, dtype=np.float32)
    }


def create_test_smpl_params():
    """创建测试SMPL参数文件"""
    n_frames = 5
    
    # 创建简单的SMPL参数
    global_orient = np.random.randn(n_frames, 3) * 0.1
    body_pose = np.random.randn(n_frames, 63) * 0.1
    left_hand_pose = np.zeros((n_frames, 45))
    right_hand_pose = np.zeros((n_frames, 45))
    transl = np.random.randn(n_frames, 3) * 0.1
    expression = np.random.randn(n_frames, 50) * 0.1
    jaw_pose = np.random.randn(n_frames, 3) * 0.1
    
    return {
        'global_orient': global_orient.astype(np.float32),
        'body_pose': body_pose.astype(np.float32),
        'left_hand_pose': left_hand_pose.astype(np.float32),
        'right_hand_pose': right_hand_pose.astype(np.float32),
        'transl': transl.astype(np.float32),
        'expression': expression.astype(np.float32),
        'jaw_pose': jaw_pose.astype(np.float32)
    }


def test_npz_simple_script():
    """测试简单NPZ转换脚本"""
    print("\n=== 测试 npz_to_ply_simple.py ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建测试文件
        single_frame_file = os.path.join(temp_dir, "single_frame.npz")
        multi_frame_file = os.path.join(temp_dir, "multi_frame.npz")
        output_dir = os.path.join(temp_dir, "output")
        
        # 保存测试数据
        np.savez(single_frame_file, **create_test_npz_single_frame())
        np.savez(multi_frame_file, **create_test_npz_multi_frame())
        
        print(f"Created test files in: {temp_dir}")
        
        # 测试单帧文件
        print("\n--- 测试单帧文件 ---")
        try:
            from npz_to_ply_simple import convert_npz_to_ply
            convert_npz_to_ply(single_frame_file, os.path.join(output_dir, "single"), 
                             color_mode='position')
            print("✓ 单帧转换成功")
        except Exception as e:
            print(f"✗ 单帧转换失败: {e}")
        
        # 测试多帧文件
        print("\n--- 测试多帧文件 ---")
        try:
            from npz_to_ply_simple import convert_npz_to_ply
            convert_npz_to_ply(multi_frame_file, os.path.join(output_dir, "multi"),
                             frame_start=0, frame_end=5, color_mode='height')
            print("✓ 多帧转换成功")
        except Exception as e:
            print(f"✗ 多帧转换失败: {e}")
        
        # 测试分析功能
        print("\n--- 测试文件分析 ---")
        try:
            from npz_to_ply_simple import analyze_npz_structure
            print("单帧文件分析:")
            analyze_npz_structure(single_frame_file)
            print("\n多帧文件分析:")
            analyze_npz_structure(multi_frame_file)
            print("✓ 文件分析成功")
        except Exception as e:
            print(f"✗ 文件分析失败: {e}")


def test_gaussian_model_methods():
    """测试GaussianModel的PLY保存方法"""
    print("\n=== 测试 GaussianModel PLY方法 ===")
    
    try:
        from scene.gaussian_model import GaussianModel
        
        # 创建一个简单的GaussianModel实例用于测试
        gaussians = GaussianModel()
        
        # 检查方法是否存在
        methods_to_check = [
            'save_gaussian_sequence_to_ply',
            'save_current_frame_to_ply',
            '_parse_smpl_params',
            '_set_frame_params',
            '_save_standard_ply',
            '_save_simple_ply'
        ]
        
        for method_name in methods_to_check:
            if hasattr(gaussians, method_name):
                print(f"✓ 方法 {method_name} 存在")
            else:
                print(f"✗ 方法 {method_name} 不存在")
        
        # 测试SMPL参数解析
        print("\n--- 测试SMPL参数解析 ---")
        with tempfile.TemporaryDirectory() as temp_dir:
            smpl_file = os.path.join(temp_dir, "test_smpl.npz")
            np.savez(smpl_file, **create_test_smpl_params())
            
            try:
                smpl_data = np.load(smpl_file, allow_pickle=True)
                poses, transl, expressions, jaw_poses = gaussians._parse_smpl_params(smpl_data)
                print(f"✓ SMPL参数解析成功: poses={poses.shape}, transl={transl.shape}")
            except Exception as e:
                print(f"✗ SMPL参数解析失败: {e}")
                
    except ImportError as e:
        print(f"✗ 无法导入GaussianModel: {e}")
    except Exception as e:
        print(f"✗ GaussianModel测试失败: {e}")


def test_export_script():
    """测试通用导出脚本"""
    print("\n=== 测试 export_npz_to_ply.py ===")
    
    try:
        # 检查脚本是否存在
        script_path = "export_npz_to_ply.py"
        if os.path.exists(script_path):
            print(f"✓ 脚本文件存在: {script_path}")
            
            # 尝试导入主要函数
            try:
                from export_npz_to_ply import export_from_npz_direct, analyze_npz_structure
                print("✓ 主要函数导入成功")
                
                # 测试direct模式
                with tempfile.TemporaryDirectory() as temp_dir:
                    test_file = os.path.join(temp_dir, "test.npz")
                    output_dir = os.path.join(temp_dir, "output")
                    
                    np.savez(test_file, **create_test_npz_multi_frame())
                    
                    try:
                        export_from_npz_direct(test_file, output_dir, 
                                             frame_start=0, frame_end=3, color_mode='position')
                        print("✓ Direct模式导出测试成功")
                    except Exception as e:
                        print(f"✗ Direct模式导出测试失败: {e}")
                        
            except ImportError as e:
                print(f"✗ 函数导入失败: {e}")
        else:
            print(f"✗ 脚本文件不存在: {script_path}")
            
    except Exception as e:
        print(f"✗ 导出脚本测试失败: {e}")


def main():
    """运行所有测试"""
    print("开始PLY导出功能测试...")
    
    # 测试各个组件
    test_npz_simple_script()
    test_gaussian_model_methods()
    test_export_script()
    
    print("\n=== 测试总结 ===")
    print("所有测试完成。请查看上面的输出了解各功能的状态。")
    print("\n如果看到 ✓ 表示功能正常")
    print("如果看到 ✗ 表示功能有问题，需要检查相关代码")
    
    print("\n=== 使用建议 ===")
    print("1. 对于简单的NPZ转PLY，使用: python npz_to_ply_simple.py")
    print("2. 对于Gaussian模型导出，使用: python export_npz_to_ply.py --mode gaussian")
    print("3. 对于直接NPZ导出，使用: python export_npz_to_ply.py --mode direct")
    print("4. 查看详细说明: cat PLY_EXPORT_README.md")


if __name__ == "__main__":
    main()
