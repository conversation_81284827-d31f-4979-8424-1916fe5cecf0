#!/usr/bin/env python3
"""
从NPZ文件导出PLY序列的通用脚本
支持两种模式：
1. 从训练好的Gaussian模型 + SMPL参数导出
2. 直接从NPZ文件中的点云数据导出

用法:
python export_npz_to_ply.py --mode gaussian --model_dir MODEL_DIR --smpl_params SMPL_PARAMS.npz --output_dir OUTPUT_DIR
python export_npz_to_ply.py --mode direct --npz_file POINTCLOUD.npz --output_dir OUTPUT_DIR
"""

import os
import sys
import torch
import numpy as np
from argparse import ArgumentParser
from omegaconf import OmegaConf

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scene.gaussian_model import GaussianModel
from utils.config_utils import Config
from utils.general_utils import storePly
from utils.smpl_utils import init_smpl


def load_gaussian_model(model_dir):
    """加载训练好的Gaussian模型"""
    print(f"Loading Gaussian model from: {model_dir}")
    
    # 加载配置
    config_path = os.path.join(model_dir, 'config.yaml')
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Config file not found: {config_path}")
    
    config = OmegaConf.load(config_path)
    
    # 初始化SMPL模型
    smpl_pkl_path = config.smpl_pkl_path
    if not os.path.isabs(smpl_pkl_path):
        smpl_pkl_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), smpl_pkl_path)
    
    print(f"Initializing SMPL model from: {smpl_pkl_path}")
    init_smpl(smpl_pkl_path)
    
    # 查找最新的checkpoint
    checkpoint_files = [f for f in os.listdir(model_dir) if f.startswith('chkpnt') and f.endswith('.pth')]
    if not checkpoint_files:
        raise FileNotFoundError(f"No checkpoint files found in {model_dir}")
    
    checkpoint_files.sort(key=lambda x: int(x.replace('chkpnt', '').replace('.pth', '')))
    latest_checkpoint = checkpoint_files[-1]
    checkpoint_path = os.path.join(model_dir, latest_checkpoint)
    
    print(f"Loading checkpoint: {latest_checkpoint}")
    
    # 创建模型并加载
    gaussians = GaussianModel()
    checkpoint = torch.load(checkpoint_path, map_location='cuda', weights_only=False)
    gaussians.restore(checkpoint)
    
    # 设置测试模式
    gaussians.is_test = True
    gaussians.prepare_test()
    
    print(f"Model loaded successfully. Number of Gaussian points: {gaussians._xyz.shape[0]}")
    return gaussians


def export_from_gaussian_model(gaussians, smpl_params_path, output_dir, frame_start=0, frame_end=None, frame_step=1, format_type='simple', color_mode='sh'):
    """从Gaussian模型导出PLY序列"""
    print(f"Exporting PLY sequence from Gaussian model...")
    
    gaussians.save_gaussian_sequence_to_ply(
        smpl_params_path=smpl_params_path,
        output_dir=output_dir,
        frame_start=frame_start,
        frame_end=frame_end,
        frame_step=frame_step,
        format_type=format_type
    )


def export_from_npz_direct(npz_file, output_dir, frame_start=0, frame_end=None, frame_step=1, color_mode='position'):
    """直接从NPZ文件导出PLY序列"""
    print(f"Loading point cloud data from: {npz_file}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载NPZ文件
    data = np.load(npz_file, allow_pickle=True)
    print("Available keys in NPZ file:", list(data.keys()))
    
    # 尝试不同的点云数据键名
    point_cloud_key = None
    for key in ['points', 'vertices', 'xyz', 'positions', 'point_cloud']:
        if key in data:
            point_cloud_key = key
            break
    
    if point_cloud_key is None:
        raise ValueError(f"No point cloud data found in NPZ file. Available keys: {list(data.keys())}")
    
    points = data[point_cloud_key]  # 应该是 [N_frames, N_points, 3] 或 [N_points, 3]
    
    # 处理不同的数据格式
    if points.ndim == 2:
        # 单帧数据，复制为多帧
        points = points[np.newaxis, ...]
        print(f"Single frame data detected, shape: {points.shape}")
    elif points.ndim == 3:
        print(f"Multi-frame data detected, shape: {points.shape}")
    else:
        raise ValueError(f"Unsupported point cloud data shape: {points.shape}")
    
    # 尝试加载颜色数据
    colors = None
    for color_key in ['colors', 'rgb', 'color']:
        if color_key in data:
            colors = data[color_key]
            break
    
    # 确定帧范围
    total_frames = points.shape[0]
    if frame_end is None:
        frame_end = total_frames
    frame_end = min(frame_end, total_frames)
    
    print(f"Exporting frames {frame_start} to {frame_end-1}, step {frame_step}")
    print(f"Total {len(range(frame_start, frame_end, frame_step))} PLY files")
    
    for frame_idx in range(frame_start, frame_end, frame_step):
        xyz = points[frame_idx]  # [N_points, 3]
        
        # 生成颜色
        if colors is not None and colors.ndim >= 2:
            if colors.ndim == 3:  # [N_frames, N_points, 3]
                frame_colors = colors[frame_idx]
            else:  # [N_points, 3]
                frame_colors = colors
            
            # 确保颜色值在0-255范围内
            if frame_colors.max() <= 1.0:
                frame_colors = (frame_colors * 255).astype(np.uint8)
            else:
                frame_colors = frame_colors.astype(np.uint8)
        else:
            # 根据着色模式生成颜色
            if color_mode == 'position':
                frame_colors = np.clip((xyz - xyz.min()) / (xyz.max() - xyz.min()) * 255, 0, 255).astype(np.uint8)
            elif color_mode == 'uniform':
                frame_colors = np.full((len(xyz), 3), 255, dtype=np.uint8)
            elif color_mode == 'random':
                frame_colors = np.random.randint(0, 256, (len(xyz), 3), dtype=np.uint8)
            else:  # 默认位置着色
                frame_colors = np.clip((xyz - xyz.min()) / (xyz.max() - xyz.min()) * 255, 0, 255).astype(np.uint8)
        
        # 保存PLY文件
        ply_path = os.path.join(output_dir, f"frame_{frame_idx:06d}.ply")
        storePly(ply_path, xyz, frame_colors)
        
        if frame_idx % 50 == 0:
            print(f"Exported frame {frame_idx}")
    
    print(f"Complete! All PLY files saved to: {output_dir}")


def main():
    parser = ArgumentParser(description="从NPZ文件导出PLY序列")
    parser.add_argument('--mode', type=str, required=True, choices=['gaussian', 'direct'],
                       help='导出模式: gaussian (从Gaussian模型) 或 direct (直接从NPZ)')
    parser.add_argument('--output_dir', type=str, required=True, help='输出目录')
    parser.add_argument('--frame_start', type=int, default=0, help='起始帧 (默认: 0)')
    parser.add_argument('--frame_end', type=int, default=None, help='结束帧 (默认: 到最后一帧)')
    parser.add_argument('--frame_step', type=int, default=1, help='帧步长 (默认: 1)')
    
    # Gaussian模式参数
    parser.add_argument('--model_dir', type=str, help='Gaussian模型目录路径 (gaussian模式必需)')
    parser.add_argument('--smpl_params', type=str, help='SMPL参数文件路径 (gaussian模式必需)')
    parser.add_argument('--format_type', type=str, default='simple', choices=['standard', 'simple'],
                       help='PLY格式类型 (gaussian模式): standard 或 simple (默认: simple)')
    
    # Direct模式参数
    parser.add_argument('--npz_file', type=str, help='NPZ文件路径 (direct模式必需)')
    
    # 通用参数
    parser.add_argument('--color_mode', type=str, default='sh',
                       choices=['position', 'opacity', 'sh', 'uniform', 'random'],
                       help='着色模式 (默认: sh for gaussian, position for direct)')
    
    args = parser.parse_args()
    
    # 验证参数
    if args.mode == 'gaussian':
        if not args.model_dir or not args.smpl_params:
            parser.error("gaussian模式需要 --model_dir 和 --smpl_params 参数")
        if not os.path.exists(args.model_dir):
            print(f"错误: 模型目录不存在: {args.model_dir}")
            return
        if not os.path.exists(args.smpl_params):
            print(f"错误: SMPL参数文件不存在: {args.smpl_params}")
            return
    elif args.mode == 'direct':
        if not args.npz_file:
            parser.error("direct模式需要 --npz_file 参数")
        if not os.path.exists(args.npz_file):
            print(f"错误: NPZ文件不存在: {args.npz_file}")
            return
    
    try:
        if args.mode == 'gaussian':
            # 从Gaussian模型导出
            gaussians = load_gaussian_model(args.model_dir)
            export_from_gaussian_model(
                gaussians=gaussians,
                smpl_params_path=args.smpl_params,
                output_dir=args.output_dir,
                frame_start=args.frame_start,
                frame_end=args.frame_end,
                frame_step=args.frame_step,
                format_type=args.format_type,
                color_mode=args.color_mode
            )
        else:  # direct
            # 直接从NPZ导出
            color_mode = args.color_mode if args.color_mode != 'sh' else 'position'  # direct模式不支持sh
            export_from_npz_direct(
                npz_file=args.npz_file,
                output_dir=args.output_dir,
                frame_start=args.frame_start,
                frame_end=args.frame_end,
                frame_step=args.frame_step,
                color_mode=color_mode
            )
            
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
