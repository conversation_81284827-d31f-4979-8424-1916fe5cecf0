#!/usr/bin/env python3
"""
修复PLY文件中的Gaussian Splatting属性
读取现有的PLY文件，修复缩放、旋转和透明度问题，然后重新保存
"""

import os
import sys
import numpy as np
from argparse import ArgumentParser

try:
    from plyfile import PlyData, PlyElement
except ImportError:
    print("Error: plyfile package is required. Install with: pip install plyfile")
    sys.exit(1)


def fix_gaussian_attributes(opacities, scales, rotations):
    """修复Gaussian Splatting属性"""
    
    # 1. 修复透明度：确保在[0,1]范围内
    if opacities.min() < 0 or opacities.max() > 1:
        # 如果不在[0,1]范围，应用sigmoid激活
        opacities = np.clip(opacities, -10, 10)
        opacities = 1.0 / (1.0 + np.exp(-opacities))
    
    # 2. 修复缩放：限制最大缩放值
    if scales.max() > 1.0:  # 如果缩放值过大
        # 可能是log空间的，先exp再限制
        if scales.min() < 0:  # 确实是log空间
            scales = np.exp(scales)
        scales = np.clip(scales, 1e-6, 0.05)  # 更严格的限制
    else:
        # 已经是正常空间，只需限制上限
        scales = np.clip(scales, 1e-6, 0.05)
    
    # 3. 修复旋转：确保四元数归一化
    rotation_norms = np.linalg.norm(rotations, axis=1, keepdims=True)
    rotation_norms = np.maximum(rotation_norms, 1e-8)
    rotations = rotations / rotation_norms
    
    return opacities, scales, rotations


def analyze_ply_file(ply_path):
    """分析PLY文件的属性"""
    print(f"分析文件: {ply_path}")
    
    plydata = PlyData.read(ply_path)
    vertices = plydata['vertex']
    
    # 提取属性
    xyz = np.column_stack([vertices['x'], vertices['y'], vertices['z']])
    
    # 检查是否有Gaussian Splatting属性
    has_opacity = 'opacity' in vertices.dtype.names
    has_scale = all(f'scale_{i}' in vertices.dtype.names for i in range(3))
    has_rotation = all(f'rot_{i}' in vertices.dtype.names for i in range(4))
    
    print(f"  点数: {len(vertices)}")
    print(f"  位置范围: X[{xyz[:, 0].min():.3f}, {xyz[:, 0].max():.3f}], Y[{xyz[:, 1].min():.3f}, {xyz[:, 1].max():.3f}], Z[{xyz[:, 2].min():.3f}, {xyz[:, 2].max():.3f}]")
    
    if has_opacity:
        opacity = vertices['opacity']
        print(f"  透明度范围: [{opacity.min():.4f}, {opacity.max():.4f}], 均值: {opacity.mean():.4f}")
    
    if has_scale:
        scales = np.column_stack([vertices[f'scale_{i}'] for i in range(3)])
        print(f"  缩放范围: [{scales.min():.4f}, {scales.max():.4f}], 均值: {scales.mean():.4f}")
    
    if has_rotation:
        rotations = np.column_stack([vertices[f'rot_{i}'] for i in range(4)])
        rotation_norms = np.linalg.norm(rotations, axis=1)
        print(f"  旋转四元数模长范围: [{rotation_norms.min():.4f}, {rotation_norms.max():.4f}]")
    
    return has_opacity, has_scale, has_rotation


def fix_ply_file(input_path, output_path, scale_limit=0.05):
    """修复PLY文件"""
    print(f"\n修复文件: {input_path} -> {output_path}")
    
    # 读取原文件
    plydata = PlyData.read(input_path)
    vertices = plydata['vertex']
    
    # 检查属性
    has_opacity = 'opacity' in vertices.dtype.names
    has_scale = all(f'scale_{i}' in vertices.dtype.names for i in range(3))
    has_rotation = all(f'rot_{i}' in vertices.dtype.names for i in range(4))
    
    if not (has_opacity and has_scale and has_rotation):
        print("  警告: 不是标准的Gaussian Splatting PLY文件，跳过修复")
        return False
    
    # 提取属性
    opacities = vertices['opacity']
    scales = np.column_stack([vertices[f'scale_{i}'] for i in range(3)])
    rotations = np.column_stack([vertices[f'rot_{i}'] for i in range(4)])
    
    # 修复属性
    opacities_fixed, scales_fixed, rotations_fixed = fix_gaussian_attributes(opacities, scales, rotations)
    
    # 应用缩放限制参数
    scales_fixed = np.clip(scales_fixed, 1e-6, scale_limit)
    
    print(f"  修复前 - 透明度: [{opacities.min():.4f}, {opacities.max():.4f}], 缩放: [{scales.min():.4f}, {scales.max():.4f}]")
    print(f"  修复后 - 透明度: [{opacities_fixed.min():.4f}, {opacities_fixed.max():.4f}], 缩放: [{scales_fixed.min():.4f}, {scales_fixed.max():.4f}]")
    
    # 创建新的顶点数据
    new_vertices = vertices.copy()
    new_vertices['opacity'] = opacities_fixed
    for i in range(3):
        new_vertices[f'scale_{i}'] = scales_fixed[:, i]
    for i in range(4):
        new_vertices[f'rot_{i}'] = rotations_fixed[:, i]
    
    # 保存修复后的文件
    new_plydata = PlyData([PlyElement.describe(new_vertices, 'vertex')])
    new_plydata.write(output_path)
    
    print(f"  修复完成，保存到: {output_path}")
    return True


def batch_fix_directory(input_dir, output_dir, scale_limit=0.05):
    """批量修复目录中的PLY文件"""
    print(f"\n批量修复目录: {input_dir} -> {output_dir}")
    
    os.makedirs(output_dir, exist_ok=True)
    
    ply_files = [f for f in os.listdir(input_dir) if f.endswith('.ply')]
    ply_files.sort()
    
    print(f"找到 {len(ply_files)} 个PLY文件")
    
    success_count = 0
    for ply_file in ply_files:
        input_path = os.path.join(input_dir, ply_file)
        output_path = os.path.join(output_dir, ply_file)
        
        if fix_ply_file(input_path, output_path, scale_limit):
            success_count += 1
    
    print(f"\n批量修复完成: {success_count}/{len(ply_files)} 个文件成功修复")


def main():
    parser = ArgumentParser(description="修复Gaussian Splatting PLY文件的属性")
    parser.add_argument('input', type=str, help='输入PLY文件或目录')
    parser.add_argument('output', type=str, help='输出PLY文件或目录')
    parser.add_argument('--scale-limit', type=float, default=0.05, 
                       help='缩放值上限 (默认: 0.05)')
    parser.add_argument('--analyze-only', action='store_true',
                       help='只分析文件，不进行修复')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"错误: 输入路径不存在: {args.input}")
        return
    
    if args.analyze_only:
        # 只分析
        if os.path.isfile(args.input):
            analyze_ply_file(args.input)
        else:
            ply_files = [f for f in os.listdir(args.input) if f.endswith('.ply')]
            for ply_file in sorted(ply_files)[:3]:  # 只分析前3个
                analyze_ply_file(os.path.join(args.input, ply_file))
    else:
        # 修复
        if os.path.isfile(args.input):
            # 单文件修复
            fix_ply_file(args.input, args.output, args.scale_limit)
        else:
            # 目录批量修复
            batch_fix_directory(args.input, args.output, args.scale_limit)


if __name__ == "__main__":
    main()
